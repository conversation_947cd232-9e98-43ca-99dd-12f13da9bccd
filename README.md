# CcbLife 后端服务

基于 Spring Boot 的自动化测试管理系统后端API

## 技术栈

- Spring Boot + MyBatis + PostgreSQL
- Docker + Docker Compose
- JWT + Spring Security

## 快速启动

```bash
# 1. 启动服务 (Docker)
./ccblife.sh

# 2. 访问API
curl http://localhost:8090/captchaImage
```

**默认账号:** admin/admin123

## 配置

编辑 `.env` 文件：
```bash
APP_PORT=8090                # 后端端口
DB_PORT=5432                 # 数据库端口
POSTGRES_PASSWORD=123456     # 数据库密码
```

## 常用命令

```bash
# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f ccblifeserver

# 停止服务
docker-compose down

# 数据库备份
docker exec ccblife-postgres pg_dump -U postgres ccblife > backup.sql
```

## 部署方式

### Docker (推荐)
```bash
./ccblife.sh          # Linux/Mac
.\ccblife.ps1         # Windows PowerShell
```

### 传统JAR
```bash
./ccblife.sh jar      # Linux/Mac
ccblife.bat           # Windows (交互式)
```

## 主要接口

- `GET /captchaImage` - 获取验证码
- `POST /login` - 用户登录
- `/system/user/*` - 用户管理
- `/system/role/*` - 角色管理

## 故障排除

- **验证码不显示**: 参考 `CAPTCHA_FIX_README.md`
- **端口冲突**: 修改 `.env` 中的端口配置
- **查看日志**: `docker-compose logs -f ccblifeserver`
